package com.hean.nucleus.model.support;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_sms")
public class TSms {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long userId;

    private String mobile;

    private String message;

    private LocalDateTime sendTime;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 删除标记：1=已删除，0=未删除
     */
    private Integer deleteFlag;

    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;

}
