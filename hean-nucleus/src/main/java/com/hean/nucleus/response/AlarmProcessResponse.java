package com.hean.nucleus.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hean.common.annotation.FieldDoc;
import com.hean.nucleus.model.support.TAlarmProcess;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 告警处置响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AlarmProcessResponse extends TAlarmProcess {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime alarmTime;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 报警类型
     */
    private Integer alarmType;

    /**
     * 报警动作
     */
    private Integer alarmAction;

    /**
     * 视频通道
     */
    private Integer vidioChan;

    /**
     * IO输入通道
     */
    private Integer alarmInchan;

    /**
     * 硬盘编号
     */
    private Integer diskNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 服务器数据插入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime timeInserted;

}
