package com.hean.nucleus.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hean.common.exception.ServiceException;
import com.hean.nucleus.core.PageView;
import com.hean.nucleus.mapper.radpac.RadpacAlarmMapper;
import com.hean.nucleus.mapper.support.TAlarmProcessMapper;
import com.hean.nucleus.model.radpac.RadpacAlarm;
import com.hean.nucleus.model.support.TAlarmProcess;
import com.hean.nucleus.model.user.TUser;
import com.hean.nucleus.request.AlarmProcessAddRequest;
import com.hean.nucleus.request.AlarmProcessListRequest;
import com.hean.nucleus.response.AlarmProcessResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警处置服务
 */
@Slf4j
@Service
public class AlarmProcessService {

    @Resource
    private RadpacAlarmMapper radpacAlarmMapper;

    @Resource
    private TAlarmProcessMapper alarmProcessMapper;

    /**
     * 告警处置
     *
     * @param request 处置信息
     */
    public void processAlarm(AlarmProcessAddRequest request, TUser user) {
        // 检查告警是否存在
        RadpacAlarm alarm = radpacAlarmMapper.selectById(request.getAlarmId());
        if (alarm == null) {
            throw new ServiceException("告警不存在");
        }

        // 检查是否已经处置过
        TAlarmProcess existProcess = alarmProcessMapper.selectAlarmProcessDetail(request.getAlarmId());
        if (existProcess != null) {
            throw new ServiceException("该告警已经处置过");
        }

        TAlarmProcess alarmProcess = new TAlarmProcess();
        BeanUtils.copyProperties(request, alarmProcess);
        alarmProcess.setProcessUserId(user.getId());
        alarmProcess.setCreateTime(LocalDateTime.now());
        alarmProcess.setDeleteFlag(0);

        alarmProcessMapper.insert(alarmProcess);
    }

    /**
     * 删除处置
     *
     * @param alarmId 告警ID
     */
    public void deleteProcess(Long alarmId) {
        // 检查处置记录是否存在
        TAlarmProcess existProcess = alarmProcessMapper.selectAlarmProcessDetail(alarmId);
        if (existProcess == null) {
            throw new ServiceException("处置记录不存在");
        }

        // 软删除
        existProcess.setDeleteFlag(1);
        existProcess.setDeleteTime(LocalDateTime.now());
        alarmProcessMapper.updateById(existProcess);
    }

    /**
     * 分页查询处置记录
     *
     * @param request 查询条件
     * @return 处置记录列表
     */
    public PageView<AlarmProcessResponse> listProcessPage(AlarmProcessListRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        // 使用mapper的SQL查询方法
        List<AlarmProcessResponse> list = alarmProcessMapper.selectAlarmProcessList(request);
        PageInfo<TAlarmProcess> pageInfo = new PageInfo<>(list);

        return PageView.from(pageInfo, list);
    }

    /**
     * 查询处置详情
     *
     * @param alarmId 告警ID
     * @return 处置详情
     */
    public AlarmProcessResponse getProcessDetail(Long alarmId) {
        AlarmProcessResponse alarmProcess = alarmProcessMapper.selectAlarmProcessDetail(alarmId);
        if (alarmProcess == null) {
            throw new ServiceException("处置记录不存在");
        }

        return alarmProcess;
    }
}
