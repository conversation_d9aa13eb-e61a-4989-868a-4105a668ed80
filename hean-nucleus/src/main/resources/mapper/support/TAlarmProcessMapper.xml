<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.mapper.support.TAlarmProcessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.model.support.TAlarmProcess">
        <id column="alarm_id" property="alarmId" />
        <result column="alarm_reason" property="alarmReason" />
        <result column="process_result" property="processResult" />
        <result column="process_user_id" property="processUserId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="process_time" property="processTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

</mapper>
