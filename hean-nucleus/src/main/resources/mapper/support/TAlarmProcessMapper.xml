<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.mapper.support.TAlarmProcessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.model.support.TAlarmProcess">
        <id column="alarm_id" property="alarmId" />
        <result column="alarm_reason" property="alarmReason" />
        <result column="process_result" property="processResult" />
        <result column="process_user_id" property="processUserId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="process_time" property="processTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <resultMap id="AlarmProcessResultMap" type="com.hean.nucleus.response.AlarmProcessResponse">
        <id column="alarm_id" property="alarmId" />
        <result column="alarm_time" property="alarmTime" />
        <result column="device_id" property="deviceId" />
        <result column="alarm_type" property="alarmType" />
        <result column="alarm_action" property="alarmAction" />
        <result column="vidio_chan" property="vidioChan" />
        <result column="alarm_inchan" property="alarmInchan" />
        <result column="disk_num" property="diskNum" />
        <result column="remark" property="remark" />
        <result column="time_inserted" property="timeInserted" />
        <result column="alarm_reason" property="alarmReason" />
        <result column="process_result" property="processResult" />
        <result column="process_user_id" property="processUserId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="process_time" property="processTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 分页查询告警处置列表 -->
    <select id="selectAlarmProcessList" resultMap="BaseResultMap">
        SELECT
            a.alarm_time, a.device_id, a.alarm_type, a.alarm_action, a.vidio_chan, a.alarm_inchan, a.disk_num, a.remark, a.time_inserted,
            ap.alarm_id, ap.alarm_reason, ap.process_result, ap.process_user_id,
            ap.create_time, ap.update_time, ap.process_time, ap.delete_flag, ap.delete_time
        FROM
            t_alarm_process ap
        LEFT JOIN radpac_alarm a ON ap.alarm_id = a.alarm_id
        WHERE delete_flag = 0
        <if test="request.alarmId != null">
            AND ap.alarm_id = #{request.alarmId}
        </if>
        <if test="request.processUserId != null and request.processUserId != ''">
            AND ap.process_user_id = #{request.processUserId}
        </if>
        <if test="request.startTime != null">
            AND ap.process_time &gt;= #{request.startTime}
        </if>
        <if test="request.endTime != null">
            AND ap.process_time &lt;= #{request.endTime}
        </if>
        ORDER BY ap.process_time DESC
    </select>

    <!-- 查询告警处置详情 -->
    <select id="selectAlarmProcessDetail" resultMap="BaseResultMap">
        SELECT
            a.alarm_time, a.device_id, a.alarm_type, a.alarm_action, a.vidio_chan, a.alarm_inchan, a.disk_num, a.remark, a.time_inserted,
            ap.alarm_id, ap.alarm_reason, ap.process_result, ap.process_user_id,
            ap.create_time, ap.update_time, ap.process_time, ap.delete_flag, ap.delete_time
        FROM
            t_alarm_process ap
            LEFT JOIN radpac_alarm a ON ap.alarm_id = a.alarm_id
        WHERE
            ap.alarm_id = #{alarmId}
            AND ap.delete_flag = 0
    </select>

</mapper>
