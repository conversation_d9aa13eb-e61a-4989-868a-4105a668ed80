<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.mapper.support.TSmsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.model.support.TSms">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="mobile" property="mobile" />
        <result column="message" property="message" />
        <result column="send_time" property="sendTime" />
        <result column="create_time" property="createTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

</mapper>
