package com.hean.common.service;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import cn.hutool.core.util.HexUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.hean.common.core.redis.RedisCache;
import com.hean.common.exception.ServiceException;

import cn.hutool.crypto.SmUtil;


@Service
public class CipherService {

    @Value("${cipher.prefix}")
    private String prefix;

    @Value("${cipher.clientId}")
    private String clientId;

    @Value("${cipher.clientSecret}")
    private String clientSecret;

    @Value("${cipher.serverId}")
    private String serverId;

    @Value("${cipher.applicationId}")
    private String applicationId;

    @Value("${cipher.appSecret}")
    private String appSecret;

    @Value("${cipher.sm2KeyId}")
    private String sm2KeyId;

    @Value("${cipher.sm3KeyId}")
    private String sm3KeyId;

    @Value("${cipher.sm4KeyId}")
    private String sm4KeyId;

    @Resource
    private RedisCache redisCache;

    private JSONObject postForm(String path, String authorization, Map<String, String> params) {
        HttpRequest request = HttpRequest.post(prefix + path)
                .header("Content-Type", "application/x-www-form-urlencoded;charset=utf-8")
                .header("Authorization", authorization)
                .formStr(params);

        try(HttpResponse response = request.execute()) {
            if(!response.isOk()) {
                throw new ServiceException("请求失败");
            }
            return JSON.parseObject(response.body());
        } catch (Exception e) {
            throw new ServiceException("请求失败", e);
        }
    }

    private JSONObject postJsonMap(String path, String authorization, HashMap<String, Object> params) {
        HttpRequest request = HttpRequest.post(prefix + path)
                .header("Content-Type", "application/json;charset=utf-8")
                .header("Authorization", authorization)
                .body(JSON.toJSONString(params));

        try(HttpResponse response = request.execute()) {
            if(!response.isOk()) {
                throw new ServiceException("请求失败");
            }
            return JSON.parseObject(response.body());
        } catch (Exception e) {
            throw new ServiceException("请求失败", e);
        }
    }

    private synchronized String getAccessToken() {
        String accessToken = redisCache.getCacheObject("cipher:accessToken");
        if(accessToken != null) {
            return "Bearer " + accessToken;
        }

        Long timestamp = System.currentTimeMillis();

        JSONObject credentials = new JSONObject();
        credentials.put("algorithm", "15843-4-5.1.1");
        credentials.put("response", Base64.getEncoder().encodeToString(HexUtil.decodeHex(SmUtil.sm3(timestamp + serverId + appSecret))));
        credentials.put("user_id", applicationId);
        credentials.put("server_id", serverId);
        credentials.put("timestamp", timestamp);

        accessToken = postForm(
                "/auth_center/cipher/token",
                "Basic " + Base64.getEncoder().encodeToString((clientId + ":" + clientSecret).getBytes(StandardCharsets.UTF_8)),
                Map.of("grant_type", "password", "credentials", Base64.getEncoder().encodeToString(credentials.toJSONString().getBytes(StandardCharsets.UTF_8)))
        ).getString("access_token");
        redisCache.setCacheObject("cipher:accessToken", accessToken, 800, TimeUnit.SECONDS);
        return "Bearer " + accessToken;
    }


    public String sm2Encrypt(String value) {
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> dataMap = new HashMap<>();
        // hex 编码
//        dataMap.put("secretKeyCode", sm2KeyId);
//        dataMap.put("algorithm", 2);
//        dataMap.put("requestEncode", 1);
//        dataMap.put("responseEncode", 1);
//        dataMap.put("mode", 0);
//        dataMap.put("iv", HexUtil.encodeHexStr(new byte[16]));
//        dataMap.put("plainText", HexUtil.encodeHexStr(value.getBytes(StandardCharsets.UTF_8)));
//        params.put("alg", 0);
//        params.put("data", dataMap);

        // base64 编码
        dataMap.put("secretKeyCode", sm2KeyId);
        dataMap.put("algorithm", 2);
        dataMap.put("requestEncode", 2);
        dataMap.put("responseEncode", 2);
        dataMap.put("mode", 0);
        dataMap.put("iv", HexUtil.encodeHexStr(new byte[16]));
        dataMap.put("plainText", Base64.getEncoder().encodeToString(value.getBytes(StandardCharsets.UTF_8)));
        params.put("alg", 0);
        params.put("data", dataMap);
        JSONObject jsonObject = postJsonMap("/cloud_cipher_svs/dataEncrypt", getAccessToken(), params);
        try {
            JSONObject contentJson = jsonObject.getJSONObject("content");
            return contentJson.getString("data");
        } catch (Exception e) {
            throw new ServiceException("数据处理失败: " + jsonObject.toJSONString(), e);
        }
    }

    public String sm2Decrypt(String encryptData) {
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> dataMap = new HashMap<>();

        // hex 编码
//        dataMap.put("secretKeyCode", sm2KeyId);
//        dataMap.put("algorithm", 2);
//        dataMap.put("requestEncode", 1);
//        dataMap.put("responseEncode", 1);
//        dataMap.put("iv", HexUtil.encodeHexStr(new byte[16]));
//        dataMap.put("cipherText", encryptData);
//        params.put("alg", 0);
//        params.put("data", dataMap);

        // base64 编码
        dataMap.put("secretKeyCode", sm2KeyId);
        dataMap.put("algorithm", 2);
        dataMap.put("requestEncode", 2);
        dataMap.put("responseEncode", 2);
        dataMap.put("iv", HexUtil.encodeHexStr(new byte[16]));
        dataMap.put("cipherText", encryptData);
        params.put("alg", 0);
        params.put("data", dataMap);

        JSONObject jsonObject = postJsonMap("/cloud_cipher_svs/dataDecrypt", getAccessToken(), params);
        try {
            JSONObject contentJson = jsonObject.getJSONObject("content");
//            return HexUtil.decodeHexStr(contentJson.getString("data"));
            return new String(Base64.getDecoder().decode(contentJson.getString("data")), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new ServiceException("数据处理失败: " + jsonObject.toJSONString(), e);
        }
    }

    public boolean sm2Compare(String encryptValue, String value) {
        return sm2Decrypt(encryptValue).equals(value);
    }

    public String sm4Encrypt(String value) {
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> dataMap = new HashMap<>();

        // base64 编码
        dataMap.put("secretKeyCode", sm4KeyId);
        dataMap.put("algorithm", 1);
        dataMap.put("requestEncode", 2);
        dataMap.put("responseEncode", 2);
        dataMap.put("mode", 0);
        dataMap.put("iv", HexUtil.encodeHexStr(new byte[16]));
        dataMap.put("plainText", Base64.getEncoder().encodeToString(value.getBytes(StandardCharsets.UTF_8)));
        params.put("alg", 0);
        params.put("data", dataMap);
        JSONObject jsonObject = postJsonMap("/cloud_cipher_svs/dataEncrypt", getAccessToken(), params);
        try {
            JSONObject contentJson = jsonObject.getJSONObject("content");
            return contentJson.getString("data");
        } catch (Exception e) {
            throw new ServiceException("数据处理失败: " + jsonObject.toJSONString(), e);
        }
    }

    /**
     * 解密数据并校验数据完整性
     *
     * @param encryptData 加密的数据
     * @return 解密后的数据
     */
    public String sm4Decrypt(String encryptData) {
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> dataMap = new HashMap<>();

        // base64 编码
        dataMap.put("secretKeyCode", sm4KeyId);
        dataMap.put("algorithm", 1);
        dataMap.put("requestEncode", 2);
        dataMap.put("responseEncode", 2);
        dataMap.put("iv", HexUtil.encodeHexStr(new byte[16]));
        dataMap.put("cipherText", encryptData);
        params.put("alg", 0);
        params.put("data", dataMap);

        JSONObject jsonObject = postJsonMap("/cloud_cipher_svs/dataDecrypt", getAccessToken(), params);
        try {
            JSONObject contentJson = jsonObject.getJSONObject("content");
            return new String(Base64.getDecoder().decode(contentJson.getString("data")), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new ServiceException("数据处理失败: " + jsonObject.toJSONString(), e);
        }
    }

    public boolean sm4Compare(String encryptValue, String value) {
        return sm4Decrypt(encryptValue).equals(value);
    }

    public String calcHash(String value) {
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> dataMap = new HashMap<>();

        // base64 编码
        dataMap.put("secretKeyCode", sm3KeyId);
        dataMap.put("algorithm", 5);
        dataMap.put("requestEncode", 2);
        dataMap.put("responseEncode", 2);
        dataMap.put("message", Base64.getEncoder().encodeToString(value.getBytes(StandardCharsets.UTF_8)));
        params.put("alg", 0);
        params.put("data", dataMap);
        JSONObject jsonObject = postJsonMap("/cloud_cipher_svs/dataHash", getAccessToken(), params);
        try {
            JSONObject contentJson = jsonObject.getJSONObject("content");
            return contentJson.getString("data");
        } catch (Exception e) {
            throw new ServiceException("数据处理失败: " + jsonObject.toJSONString(), e);
        }
    }

    public void checkHash(String value, String itemHash) {

        String calcHash = calcHash(value);

        System.out.println("checkHash --> " + value + "-->" + itemHash + "--> " + calcHash);

        if(!calcHash.equals(itemHash)) {
            throw new ServiceException("数据校验失败，加密数据可能被篡改");
        }
    }

}
