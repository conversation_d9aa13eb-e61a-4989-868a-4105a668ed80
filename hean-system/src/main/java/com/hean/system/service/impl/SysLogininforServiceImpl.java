package com.hean.system.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import com.hean.common.service.CipherService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.hean.system.domain.SysLogininfor;
import com.hean.system.mapper.SysLogininforMapper;
import com.hean.system.service.ISysLogininforService;

import javax.annotation.Resource;

/**
 * 系统访问日志情况信息 服务层处理
 */
@Service
public class SysLogininforServiceImpl implements ISysLogininforService {

    @Resource
    private SysLogininforMapper logininforMapper;

    @Resource
    private CipherService cipherService;

    /**
     * 新增系统登录日志
     * 
     * @param logininfor 访问日志对象
     */
    @Override
    public void insertLogininfor(SysLogininfor logininfor) {
        if (StringUtils.isNotBlank(logininfor.getUserName())) {
            logininfor.setUserName(cipherService.sm4Encrypt(logininfor.getUserName()));
        }

        if (StringUtils.isNotBlank(logininfor.getIpaddr())) {
            logininfor.setIpaddr(cipherService.sm4Encrypt(logininfor.getIpaddr()));
        }

        if (StringUtils.isNotBlank(logininfor.getMsg())) {
            logininfor.setMsg(cipherService.sm4Encrypt(logininfor.getMsg()));
        }

        logininfor.setItemHash(cipherService.calcHash(logininfor.joinItemHash()));

        logininforMapper.insertLogininfor(logininfor);
    }

    /**
     * 查询系统登录日志集合
     * 
     * @param logininfor 访问日志对象
     * @return 登录记录集合
     */
    @Override
    public List<SysLogininfor> selectLogininforList(SysLogininfor logininfor) {
        if (StringUtils.isNotBlank(logininfor.getUserName())) {
            logininfor.setUserName(cipherService.sm4Encrypt(logininfor.getUserName()));
        }

        if (StringUtils.isNotBlank(logininfor.getIpaddr())) {
            logininfor.setIpaddr(cipherService.sm4Encrypt(logininfor.getIpaddr()));
        }

        if (StringUtils.isNotBlank(logininfor.getMsg())) {
            logininfor.setMsg(cipherService.sm4Encrypt(logininfor.getMsg()));
        }

        return logininforMapper.selectLogininforList(logininfor).stream().peek(m -> {

            // 核验商密密文HASH
            cipherService.checkHash(m.joinItemHash(), m.getItemHash());

            if(StringUtils.isNotBlank(m.getUserName())) {
                m.setUserName(cipherService.sm4Decrypt(m.getUserName()));
            }

            if(StringUtils.isNotBlank(m.getIpaddr())) {
                m.setIpaddr(cipherService.sm4Decrypt(m.getIpaddr()));
            }

            if(StringUtils.isNotBlank(m.getMsg())) {
                m.setMsg(cipherService.sm4Decrypt(m.getMsg()));
            }
        }).collect(Collectors.toList());
    }

    /**
     * 批量删除系统登录日志
     * 
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    @Override
    public int deleteLogininforByIds(Long[] infoIds)
    {
        return logininforMapper.deleteLogininforByIds(infoIds);
    }

    /**
     * 清空系统登录日志
     */
    @Override
    public void cleanLogininfor()
    {
        logininforMapper.cleanLogininfor();
    }
}
