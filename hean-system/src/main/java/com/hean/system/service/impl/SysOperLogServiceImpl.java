package com.hean.system.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import com.hean.common.service.CipherService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.hean.system.domain.SysOperLog;
import com.hean.system.mapper.SysOperLogMapper;
import com.hean.system.service.ISysOperLogService;

import javax.annotation.Resource;

/**
 * 操作日志 服务层处理

 */
@Service
public class SysOperLogServiceImpl implements ISysOperLogService {

    @Resource
    private SysOperLogMapper operLogMapper;

    @Resource
    private CipherService cipherService;

    /**
     * 新增操作日志
     * 
     * @param operLog 操作日志对象
     */
    @Override
    public void insertOperlog(SysOperLog operLog) {
        if(StringUtils.isNotBlank(operLog.getOperName())) {
            operLog.setOperName(cipherService.sm4Encrypt(operLog.getOperName()));
        }

        if(StringUtils.isNotBlank(operLog.getOperUrl())) {
            operLog.setOperUrl(cipherService.sm4Encrypt(operLog.getOperUrl()));
        }

        if(StringUtils.isNotBlank(operLog.getOperParam())) {
            operLog.setOperParam(cipherService.sm4Encrypt(operLog.getOperParam()));
        }

        if (StringUtils.isNotBlank(operLog.getJsonResult())) {
            operLog.setJsonResult(cipherService.sm4Encrypt(operLog.getJsonResult()));
        }

        operLog.setItemHash(cipherService.calcHash(operLog.joinItemHash()));

        operLogMapper.insertOperlog(operLog);
    }

    /**
     * 查询系统操作日志集合
     * 
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    @Override
    public List<SysOperLog> selectOperLogList(SysOperLog operLog) {
        return operLogMapper.selectOperLogList(operLog).stream().peek(m -> {

            // 核验商密密文HASH
            cipherService.checkHash(m.joinItemHash(), m.getItemHash());

            if(StringUtils.isNotBlank(m.getOperName())) {
                m.setOperName(cipherService.sm4Decrypt(m.getOperName()));
            }

            if(StringUtils.isNotBlank(m.getOperUrl())) {
                m.setOperUrl(cipherService.sm4Decrypt(m.getOperUrl()));
            }

            if(StringUtils.isNotBlank(m.getOperParam())) {
                m.setOperParam(cipherService.sm4Decrypt(m.getOperParam()));
            }

            if(StringUtils.isNotBlank(m.getJsonResult())) {
                m.setJsonResult(cipherService.sm4Decrypt(m.getJsonResult()));
            }
        }).collect(Collectors.toList());
    }

    /**
     * 批量删除系统操作日志
     * 
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    @Override
    public int deleteOperLogByIds(Long[] operIds) {
        return operLogMapper.deleteOperLogByIds(operIds);
    }

    /**
     * 查询操作日志详细
     * 
     * @param operId 操作ID
     * @return 操作日志对象
     */
    @Override
    public SysOperLog selectOperLogById(Long operId) {
        SysOperLog operLog = operLogMapper.selectOperLogById(operId);
        if(operLog == null) {
            return null;
        }

        // 核验商密密文HASH
        cipherService.checkHash(operLog.joinItemHash(), operLog.getItemHash());

        if(StringUtils.isNotBlank(operLog.getOperName())) {
            operLog.setOperName(cipherService.sm4Decrypt(operLog.getOperName()));
        }

        if(StringUtils.isNotBlank(operLog.getOperUrl())) {
            operLog.setOperUrl(cipherService.sm4Decrypt(operLog.getOperUrl()));
        }

        if(StringUtils.isNotBlank(operLog.getOperParam())) {
            operLog.setOperParam(cipherService.sm4Decrypt(operLog.getOperParam()));
        }

        if(StringUtils.isNotBlank(operLog.getJsonResult())) {
            operLog.setJsonResult(cipherService.sm4Decrypt(operLog.getJsonResult()));
        }

        return operLog;
    }

    /**
     * 清空操作日志
     */
    @Override
    public void cleanOperLog() {
        operLogMapper.cleanOperLog();
    }
}
